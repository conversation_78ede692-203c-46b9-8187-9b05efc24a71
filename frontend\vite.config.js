import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
// import eslint from 'vite-plugin-eslint'
// import { createMockPlugin } from 'vite-plugin-mock'

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')

  return {
    plugins: [
      vue(),

      // ESLint插件
      // eslint({
      //   include: ['src/**/*.vue', 'src/**/*.js', 'src/**/*.ts'],
      //   exclude: ['node_modules', 'dist']
      // }),

      // 自动导入
      AutoImport({
        imports: ['vue', 'vue-router', 'pinia'],
        resolvers: [ElementPlusResolver()],
        dts: true,
        eslintrc: {
          enabled: true
        }
      }),

      // 组件自动导入
      Components({
        resolvers: [ElementPlusResolver()],
        dts: true
      }),

      // Mock插件（开发环境）
      // createMockPlugin({
      //   mockPath: 'mock',
      //   localEnabled: mode === 'development',
      //   prodEnabled: false,
      //   logger: true
      // })
    ],

    // 路径解析
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '~': resolve(__dirname, 'src'),
        'components': resolve(__dirname, 'src/components'),
        'views': resolve(__dirname, 'src/views'),
        'utils': resolve(__dirname, 'src/utils'),
        'services': resolve(__dirname, 'src/services'),
        'composables': resolve(__dirname, 'src/composables'),
        'assets': resolve(__dirname, 'src/assets')
      }
    },

    // CSS配置
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@import "@/styles/variables.scss";`
        }
      }
    },

    // 开发服务器配置
    server: {
      port: 3000,
      host: '0.0.0.0',
      open: true,
      cors: true,
      proxy: {
        '/api': {
          target: 'http://localhost:8001',
          changeOrigin: true,
          secure: false
        },
        '/ws': {
          target: 'ws://localhost:8001',
          ws: true,
          changeOrigin: true
        },
        '/screenshots': {
          target: 'http://localhost:8001',
          changeOrigin: true,
          secure: false
        }
      }
    },

    // 构建配置
    build: {
      target: 'es2015',
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: mode === 'development',
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: mode === 'production',
          drop_debugger: mode === 'production'
        }
      },
      rollupOptions: {
        output: {
          chunkFileNames: 'js/[name]-[hash].js',
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: '[ext]/[name]-[hash].[ext]',
          manualChunks: {
            'vue-vendor': ['vue', 'vue-router', 'pinia'],
            'element-plus': ['element-plus', '@element-plus/icons-vue'],
            'chart-vendor': ['echarts', 'vue-echarts'],
            'utils': ['axios', 'dayjs', 'lodash-es']
          }
        }
      },
      chunkSizeWarningLimit: 1000
    },

    // 预览配置
    preview: {
      port: 4173,
      host: '0.0.0.0'
    },

    // 环境变量
    define: {
      __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString())
    },

    // 优化配置
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        'element-plus',
        '@element-plus/icons-vue',
        'axios',
        'echarts',
        'dayjs'
      ]
    }
  }
})
