2025-07-30 21:58:03 - GakumasuBot - INFO - logger.py:109 - 日志系统初始化完成 - 级别: INFO
2025-07-30 21:58:03 - GakumasuBot - INFO - gui.py:76 - GUI启动器初始化完成
2025-07-30 21:58:03 - GakumasuBot - INFO - gui.py:235 - 启动模式: development
2025-07-30 21:58:03 - GakumasuBot - INFO - gui.py:106 - 后端端口 8000 可用
2025-07-30 21:58:03 - GakumasuBot - INFO - gui.py:116 - 前端端口 3000 可用
2025-07-30 21:58:03 - GakumasuBot - INFO - gui.py:130 - 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8000 --reload
2025-07-30 21:58:03 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:8000/health
2025-07-30 21:58:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Will watch for changes in these directories: ['J:\\笔记本专用\\代码\\Gakumasu-Bot']
2025-07-30 21:58:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
2025-07-30 21:58:03 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started reloader process [46928] using StatReload
2025-07-30 21:58:07 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started server process [56476]
2025-07-30 21:58:07 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Waiting for application startup.
2025-07-30 21:58:07 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Application startup complete.
2025-07-30 21:58:07 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55639 - "GET /health HTTP/1.1" 200 OK
2025-07-30 21:58:07 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:8000/health
2025-07-30 21:58:07 - GakumasuBot - INFO - gui.py:166 - 启动前端服务: npm run dev
2025-07-30 21:58:07 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:3000
2025-07-30 21:58:09 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:3000
2025-07-30 21:58:09 - GakumasuBot - INFO - gui.py:260 - 所有服务启动完成，按 Ctrl+C 退出
2025-07-30 21:58:13 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55710 - "WebSocket /ws" [accepted]
2025-07-30 21:58:13 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55713 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-30 21:58:13 - GakumasuBot - INFO - gui.py:146 - Backend: 未找到游戏窗口: gakumas
2025-07-30 21:58:13 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:55714 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: WebSocket发送失败: Object of type ScreenshotMode is not JSON serializable
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: [CRITICAL] 截图API端点被调用！
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 原始请求: mode=ScreenshotModeEnum.WINDOW (<enum 'ScreenshotModeEnum'>), format=ScreenshotFormatEnum.PNG (<enum 'ScreenshotFormatEnum'>)
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换后的配置: mode=window (<class 'str'>), format=png (<class 'str'>)
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 完整配置字典: {'mode': 'window', 'format': 'png', 'quality': 90, 'region': None, 'save_to_disk': True, 'filename_prefix': 'screenshot'}
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: [INFO] 开始执行截图，配置: {'mode': 'window', 'format': 'png', 'quality': 90, 'region': None, 'save_to_disk': True, 'filename_prefix': 'screenshot'}
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] _convert_screenshot_config 接收到配置: {'mode': 'window', 'format': 'png', 'quality': 90, 'region': None, 'save_to_disk': True, 'filename_prefix': 'screenshot'}
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 原始模式值: window (类型: <class 'str'>)
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 模式值来自字符串: window
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 准备转换模式字符串 'window' 为 ScreenshotMode 枚举
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 模式转换完成: window -> ScreenshotMode.WINDOW (类型: <enum 'ScreenshotMode'>)
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 原始格式值: png (类型: <class 'str'>)
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 格式值来自字符串: png
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 准备转换格式字符串 'png' 为 ScreenshotFormat 枚举
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 格式转换完成: png -> ScreenshotFormat.PNG (类型: <enum 'ScreenshotFormat'>)
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 最终转换的配置对象: mode=ScreenshotMode.WINDOW (类型: <enum 'ScreenshotMode'>), format=ScreenshotFormat.PNG
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: [CRITICAL] _capture_by_mode 接收到的模式: ScreenshotMode.WINDOW (类型: <enum 'ScreenshotMode'>)
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: [WARNING] 接收到枚举对象，值为: window
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: [INFO] 截图执行完成，成功: True, 错误: None
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:56757 - "POST /api/v1/screenshot/capture HTTP/1.1" 200 OK
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:56762 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-30 22:01:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:56767 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-30 22:10:42 - GakumasuBot - INFO - gui.py:146 - Backend: WARNING:  StatReload detected changes in 'src\web\main.py'. Reloading...
2025-07-30 22:10:42 - GakumasuBot - INFO - gui.py:281 - 正在停止所有服务...
2025-07-30 22:10:42 - GakumasuBot - INFO - gui.py:289 - 前端服务已停止
2025-07-30 22:10:42 - GakumasuBot - INFO - gui.py:301 - 后端服务已停止
2025-07-30 22:10:42 - GakumasuBot - INFO - gui.py:308 - 所有服务已停止
2025-07-30 22:16:30 - GakumasuBot - INFO - logger.py:109 - 日志系统初始化完成 - 级别: INFO
2025-07-30 22:16:30 - GakumasuBot - INFO - gui.py:76 - GUI启动器初始化完成
2025-07-30 22:16:30 - GakumasuBot - INFO - gui.py:235 - 启动模式: development
2025-07-30 22:16:30 - GakumasuBot - INFO - gui.py:106 - 后端端口 8000 可用
2025-07-30 22:16:30 - GakumasuBot - INFO - gui.py:116 - 前端端口 3000 可用
2025-07-30 22:16:30 - GakumasuBot - INFO - gui.py:130 - 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8000 --reload
2025-07-30 22:16:30 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:8000/health
2025-07-30 22:16:30 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Will watch for changes in these directories: ['J:\\笔记本专用\\代码\\Gakumasu-Bot']
2025-07-30 22:16:30 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
2025-07-30 22:16:30 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started reloader process [59688] using StatReload
2025-07-30 22:16:33 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started server process [59752]
2025-07-30 22:16:33 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Waiting for application startup.
2025-07-30 22:16:33 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Application startup complete.
2025-07-30 22:16:34 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:59857 - "GET /health HTTP/1.1" 200 OK
2025-07-30 22:16:34 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:8000/health
2025-07-30 22:16:34 - GakumasuBot - INFO - gui.py:166 - 启动前端服务: npm run dev
2025-07-30 22:16:34 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:3000
2025-07-30 22:16:36 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:3000
2025-07-30 22:16:36 - GakumasuBot - INFO - gui.py:260 - 所有服务启动完成，按 Ctrl+C 退出
2025-07-30 22:16:39 - GakumasuBot - INFO - gui.py:281 - 正在停止所有服务...
2025-07-30 22:16:39 - GakumasuBot - INFO - gui.py:289 - 前端服务已停止
2025-07-30 22:16:39 - GakumasuBot - INFO - gui.py:301 - 后端服务已停止
2025-07-30 22:16:39 - GakumasuBot - INFO - gui.py:308 - 所有服务已停止
2025-07-30 22:18:54 - GakumasuBot - INFO - logger.py:109 - 日志系统初始化完成 - 级别: INFO
2025-07-30 22:18:54 - GakumasuBot - INFO - gui.py:76 - GUI启动器初始化完成
2025-07-30 22:18:54 - GakumasuBot - INFO - gui.py:235 - 启动模式: development
2025-07-30 22:18:54 - GakumasuBot - INFO - gui.py:106 - 后端端口 8000 可用
2025-07-30 22:18:54 - GakumasuBot - INFO - gui.py:116 - 前端端口 3000 可用
2025-07-30 22:18:54 - GakumasuBot - INFO - gui.py:130 - 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8000 --reload
2025-07-30 22:18:54 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:8000/health
2025-07-30 22:18:54 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Will watch for changes in these directories: ['J:\\笔记本专用\\代码\\Gakumasu-Bot']
2025-07-30 22:18:54 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
2025-07-30 22:18:54 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started reloader process [56684] using StatReload
2025-07-30 22:18:57 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started server process [51228]
2025-07-30 22:18:57 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Waiting for application startup.
2025-07-30 22:18:57 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Application startup complete.
2025-07-30 22:18:58 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60587 - "GET /health HTTP/1.1" 200 OK
2025-07-30 22:18:58 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:8000/health
2025-07-30 22:18:58 - GakumasuBot - INFO - gui.py:166 - 启动前端服务: npm run dev
2025-07-30 22:18:58 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:3000
2025-07-30 22:19:00 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:3000
2025-07-30 22:19:00 - GakumasuBot - INFO - gui.py:260 - 所有服务启动完成，按 Ctrl+C 退出
2025-07-30 22:19:30 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60728 - "GET /api/v1/screenshot/history HTTP/1.1" 200 OK
2025-07-30 22:19:39 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60783 - "GET /api/v1/screenshot/history HTTP/1.1" 200 OK
2025-07-30 22:19:39 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60783 - "GET /favicon.ico HTTP/1.1" 404 Not Found
2025-07-30 22:19:42 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60789 - "GET /api/v1/screenshot/capture HTTP/1.1" 405 Method Not Allowed
2025-07-30 22:20:14 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60924 - "GET /screenshots/screenshot_20250730_220149_ddde1861.png HTTP/1.1" 200 OK
2025-07-30 22:20:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60955 - "GET /screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:20:33 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60986 - "WebSocket /ws" [accepted]
2025-07-30 22:20:33 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60989 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-30 22:20:33 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:60990 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-30 22:21:51 - GakumasuBot - INFO - gui.py:146 - Backend: WARNING:  StatReload detected changes in 'src\modules\screenshot_collector.py'. Reloading...
2025-07-30 22:21:51 - GakumasuBot - INFO - gui.py:281 - 正在停止所有服务...
2025-07-30 22:21:51 - GakumasuBot - INFO - gui.py:289 - 前端服务已停止
2025-07-30 22:21:51 - GakumasuBot - INFO - gui.py:301 - 后端服务已停止
2025-07-30 22:21:51 - GakumasuBot - INFO - gui.py:308 - 所有服务已停止
2025-07-30 22:22:32 - GakumasuBot - INFO - logger.py:109 - 日志系统初始化完成 - 级别: INFO
2025-07-30 22:22:32 - GakumasuBot - INFO - gui.py:76 - GUI启动器初始化完成
2025-07-30 22:22:32 - GakumasuBot - INFO - gui.py:235 - 启动模式: development
2025-07-30 22:22:32 - GakumasuBot - INFO - gui.py:106 - 后端端口 8000 可用
2025-07-30 22:22:32 - GakumasuBot - INFO - gui.py:116 - 前端端口 3000 可用
2025-07-30 22:22:32 - GakumasuBot - INFO - gui.py:130 - 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8000 --reload
2025-07-30 22:22:32 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:8000/health
2025-07-30 22:22:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Will watch for changes in these directories: ['J:\\笔记本专用\\代码\\Gakumasu-Bot']
2025-07-30 22:22:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
2025-07-30 22:22:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started reloader process [37840] using StatReload
2025-07-30 22:22:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started server process [26792]
2025-07-30 22:22:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Waiting for application startup.
2025-07-30 22:22:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Application startup complete.
2025-07-30 22:22:36 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:61602 - "GET /health HTTP/1.1" 200 OK
2025-07-30 22:22:36 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:8000/health
2025-07-30 22:22:36 - GakumasuBot - INFO - gui.py:166 - 启动前端服务: npm run dev
2025-07-30 22:22:36 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:3000
2025-07-30 22:22:38 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:3000
2025-07-30 22:22:38 - GakumasuBot - INFO - gui.py:260 - 所有服务启动完成，按 Ctrl+C 退出
2025-07-30 22:22:59 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:61731 - "WebSocket /ws" [accepted]
2025-07-30 22:22:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-30 22:22:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-30 22:22:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223457_d96561dc_thumb.png -> thumbnails/screenshot_20250728_223457_d96561dc_thumb.png
2025-07-30 22:22:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-30 22:22:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-30 22:22:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-30 22:22:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-30 22:22:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-30 22:22:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-30 22:22:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-30 22:22:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-30 22:22:59 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:61734 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-30 22:22:59 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:61735 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-30 22:23:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-30 22:23:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-30 22:23:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223457_d96561dc_thumb.png -> thumbnails/screenshot_20250728_223457_d96561dc_thumb.png
2025-07-30 22:23:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-30 22:23:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-30 22:23:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-30 22:23:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-30 22:23:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-30 22:23:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-30 22:23:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-30 22:23:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-30 22:23:08 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:61793 - "GET /api/v1/screenshot/history HTTP/1.1" 200 OK
2025-07-30 22:23:16 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:61817 - "GET /screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:25:00 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:62206 - "WebSocket /ws" [accepted]
2025-07-30 22:25:00 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-30 22:25:00 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-30 22:25:00 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223457_d96561dc_thumb.png -> thumbnails/screenshot_20250728_223457_d96561dc_thumb.png
2025-07-30 22:25:00 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-30 22:25:00 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-30 22:25:00 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-30 22:25:00 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-30 22:25:00 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-30 22:25:00 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-30 22:25:00 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-30 22:25:00 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-30 22:25:00 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:62209 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-30 22:25:00 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:62210 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-30 22:25:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败:
2025-07-30 22:25:45 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:62394 - "WebSocket /ws" [accepted]
2025-07-30 22:25:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:45 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-30 22:25:45 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-30 22:25:45 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223457_d96561dc_thumb.png -> thumbnails/screenshot_20250728_223457_d96561dc_thumb.png
2025-07-30 22:25:45 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-30 22:25:45 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-30 22:25:45 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-30 22:25:45 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-30 22:25:45 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-30 22:25:45 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-30 22:25:45 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-30 22:25:45 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-30 22:25:45 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:62398 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-30 22:25:45 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:62399 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-30 22:25:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:25:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:26:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:27:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:28:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:29:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:30:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:31:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:32:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:33:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-30 22:34:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-30 22:34:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223457_d96561dc_thumb.png -> thumbnails/screenshot_20250728_223457_d96561dc_thumb.png
2025-07-30 22:34:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-30 22:34:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-30 22:34:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-30 22:34:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-30 22:34:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-30 22:34:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-30 22:34:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-30 22:34:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-30 22:34:30 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:64296 - "GET /api/v1/screenshot/history HTTP/1.1" 200 OK
2025-07-30 22:34:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:37 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:64320 - "GET /screenshots/screenshot_20250730_220149_ddde1861.png HTTP/1.1" 200 OK
2025-07-30 22:34:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:34:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:35:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:36:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:37:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:38:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:39:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:42 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49174 - "WebSocket /ws" [accepted]
2025-07-30 22:40:42 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-30 22:40:42 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-30 22:40:42 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223457_d96561dc_thumb.png -> thumbnails/screenshot_20250728_223457_d96561dc_thumb.png
2025-07-30 22:40:42 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-30 22:40:42 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-30 22:40:42 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-30 22:40:42 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-30 22:40:42 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-30 22:40:42 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-30 22:40:42 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-30 22:40:42 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-30 22:40:42 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49179 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-30 22:40:42 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49181 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-30 22:40:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:40:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:08 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49290 - "WebSocket /ws" [accepted]
2025-07-30 22:41:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-30 22:41:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-30 22:41:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223457_d96561dc_thumb.png -> thumbnails/screenshot_20250728_223457_d96561dc_thumb.png
2025-07-30 22:41:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-30 22:41:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-30 22:41:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-30 22:41:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-30 22:41:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-30 22:41:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-30 22:41:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-30 22:41:08 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-30 22:41:08 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49293 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-30 22:41:08 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49294 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-30 22:41:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:41:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:42:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49808 - "WebSocket /ws" [accepted]
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223457_d96561dc_thumb.png -> thumbnails/screenshot_20250728_223457_d96561dc_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49811 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49812 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49818 - "GET /screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49819 - "GET /screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49820 - "GET /screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49826 - "GET /screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49824 - "GET /screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49825 - "GET /screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49832 - "GET /screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49831 - "GET /screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49836 - "GET /screenshots/thumbnails/screenshot_20250728_223457_d96561dc_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49839 - "GET /screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49841 - "GET /screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49847 - "WebSocket /ws" [accepted]
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223457_d96561dc_thumb.png -> thumbnails/screenshot_20250728_223457_d96561dc_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49850 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49851 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49855 - "GET /screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49857 - "GET /screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49856 - "GET /screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49861 - "GET /screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49873 - "GET /screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49874 - "GET /screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:25 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49875 - "GET /screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:26 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49889 - "GET /screenshots/thumbnails/screenshot_20250728_223457_d96561dc_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:26 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49888 - "GET /screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:26 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49890 - "GET /screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:26 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49898 - "GET /screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:43:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:46 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:49974 - "GET /screenshots/screenshot_20250729_000238_f7ffab32.png HTTP/1.1" 200 OK
2025-07-30 22:43:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:43:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:02 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50016 - "GET /screenshots/screenshot_20250730_220149_ddde1861.png HTTP/1.1" 200 OK
2025-07-30 22:44:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:08 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50052 - "GET /screenshots/screenshot_20250728_223457_d96561dc.png HTTP/1.1" 200 OK
2025-07-30 22:44:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:12 - GakumasuBot - INFO - gui.py:146 - Backend: WebSocket发送失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:12 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50076 - "DELETE /api/v1/screenshot/rebuilt_screenshot_20250728_223457_d96561dc HTTP/1.1" 200 OK
2025-07-30 22:44:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-30 22:44:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-30 22:44:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-30 22:44:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-30 22:44:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-30 22:44:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-30 22:44:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-30 22:44:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-30 22:44:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-30 22:44:12 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-30 22:44:12 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50079 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-30 22:44:13 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50082 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-30 22:44:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:16 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-30 22:44:16 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-30 22:44:16 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-30 22:44:16 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-30 22:44:16 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-30 22:44:16 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-30 22:44:16 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-30 22:44:16 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-30 22:44:16 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-30 22:44:16 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-30 22:44:16 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50099 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-30 22:44:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:20 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50106 - "GET /screenshots/screenshot_20250730_220149_ddde1861.png HTTP/1.1" 200 OK
2025-07-30 22:44:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: WebSocket发送失败: Object of type ScreenshotMode is not JSON serializable
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: WebSocket发送失败: Object of type ScreenshotMode is not JSON serializable
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: WebSocket发送失败: Object of type ScreenshotMode is not JSON serializable
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: WebSocket发送失败: Object of type ScreenshotMode is not JSON serializable
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [CRITICAL] 截图API端点被调用！
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 原始请求: mode=ScreenshotModeEnum.WINDOW (<enum 'ScreenshotModeEnum'>), format=ScreenshotFormatEnum.PNG (<enum 'ScreenshotFormatEnum'>)
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换后的配置: mode=window (<class 'str'>), format=png (<class 'str'>)
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 完整配置字典: {'mode': 'window', 'format': 'png', 'quality': 90, 'region': None, 'save_to_disk': True, 'filename_prefix': 'screenshot'}
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [INFO] 开始执行截图，配置: {'mode': 'window', 'format': 'png', 'quality': 90, 'region': None, 'save_to_disk': True, 'filename_prefix': 'screenshot'}
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] _convert_screenshot_config 接收到配置: {'mode': 'window', 'format': 'png', 'quality': 90, 'region': None, 'save_to_disk': True, 'filename_prefix': 'screenshot'}
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 原始模式值: window (类型: <class 'str'>)
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 模式值来自字符串: window
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 准备转换模式字符串 'window' 为 ScreenshotMode 枚举
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 模式转换完成: window -> ScreenshotMode.WINDOW (类型: <enum 'ScreenshotMode'>)
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 原始格式值: png (类型: <class 'str'>)
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 格式值来自字符串: png
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 准备转换格式字符串 'png' 为 ScreenshotFormat 枚举
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 格式转换完成: png -> ScreenshotFormat.PNG (类型: <enum 'ScreenshotFormat'>)
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 最终转换的配置对象: mode=ScreenshotMode.WINDOW (类型: <enum 'ScreenshotMode'>), format=ScreenshotFormat.PNG
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [CRITICAL] _capture_by_mode 接收到的模式: ScreenshotMode.WINDOW (类型: <enum 'ScreenshotMode'>)
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [WARNING] 接收到枚举对象，值为: window
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [INFO] 截图执行完成，成功: True, 错误: None
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50147 - "POST /api/v1/screenshot/capture HTTP/1.1" 200 OK
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50151 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50156 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50157 - "GET /screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:33 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50173 - "GET /screenshots/screenshot_20250730_224429_b8a94c07.png HTTP/1.1" 200 OK
2025-07-30 22:44:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败:
2025-07-30 22:44:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:44 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50221 - "WebSocket /ws" [accepted]
2025-07-30 22:44:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-30 22:44:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-30 22:44:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-30 22:44:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-30 22:44:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-30 22:44:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-30 22:44:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-30 22:44:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-30 22:44:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-30 22:44:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-30 22:44:44 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-07-30 22:44:44 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50226 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-30 22:44:44 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50227 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-30 22:44:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: WebSocket发送失败: Object of type ScreenshotMode is not JSON serializable
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [CRITICAL] 截图API端点被调用！
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 原始请求: mode=ScreenshotModeEnum.WINDOW (<enum 'ScreenshotModeEnum'>), format=ScreenshotFormatEnum.PNG (<enum 'ScreenshotFormatEnum'>)
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换后的配置: mode=window (<class 'str'>), format=png (<class 'str'>)
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 完整配置字典: {'mode': 'window', 'format': 'png', 'quality': 90, 'region': None, 'save_to_disk': True, 'filename_prefix': 'screenshot'}
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [INFO] 开始执行截图，配置: {'mode': 'window', 'format': 'png', 'quality': 90, 'region': None, 'save_to_disk': True, 'filename_prefix': 'screenshot'}
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] _convert_screenshot_config 接收到配置: {'mode': 'window', 'format': 'png', 'quality': 90, 'region': None, 'save_to_disk': True, 'filename_prefix': 'screenshot'}
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 原始模式值: window (类型: <class 'str'>)
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 模式值来自字符串: window
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 准备转换模式字符串 'window' 为 ScreenshotMode 枚举
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 模式转换完成: window -> ScreenshotMode.WINDOW (类型: <enum 'ScreenshotMode'>)
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 原始格式值: png (类型: <class 'str'>)
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 格式值来自字符串: png
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 准备转换格式字符串 'png' 为 ScreenshotFormat 枚举
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 格式转换完成: png -> ScreenshotFormat.PNG (类型: <enum 'ScreenshotFormat'>)
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 最终转换的配置对象: mode=ScreenshotMode.WINDOW (类型: <enum 'ScreenshotMode'>), format=ScreenshotFormat.PNG
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [CRITICAL] _capture_by_mode 接收到的模式: ScreenshotMode.WINDOW (类型: <enum 'ScreenshotMode'>)
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [WARNING] 接收到枚举对象，值为: window
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [INFO] 截图执行完成，成功: True, 错误: None
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50262 - "POST /api/v1/screenshot/capture HTTP/1.1" 200 OK
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50265 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50270 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50271 - "GET /screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:44:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:31 - GakumasuBot - INFO - logger.py:109 - 日志系统初始化完成 - 级别: INFO
2025-07-30 22:45:31 - GakumasuBot - INFO - gui.py:76 - GUI启动器初始化完成
2025-07-30 22:45:31 - GakumasuBot - INFO - gui.py:235 - 启动模式: development
2025-07-30 22:45:31 - GakumasuBot - WARNING - gui.py:103 - 后端端口 8000 被占用，使用端口 8001
2025-07-30 22:45:31 - GakumasuBot - INFO - gui.py:116 - 前端端口 3000 可用
2025-07-30 22:45:31 - GakumasuBot - INFO - gui.py:130 - 启动后端服务: uvicorn src.web.main:app --host 127.0.0.1 --port 8001 --reload
2025-07-30 22:45:31 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:8001/health
2025-07-30 22:45:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:31 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Will watch for changes in these directories: ['J:\\笔记本专用\\代码\\Gakumasu-Bot']
2025-07-30 22:45:31 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Uvicorn running on http://127.0.0.1:8001 (Press CTRL+C to quit)
2025-07-30 22:45:31 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started reloader process [63876] using StatReload
2025-07-30 22:45:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:34 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Started server process [63920]
2025-07-30 22:45:34 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Waiting for application startup.
2025-07-30 22:45:34 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     Application startup complete.
2025-07-30 22:45:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:35 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50384 - "GET /health HTTP/1.1" 200 OK
2025-07-30 22:45:35 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:8001/health
2025-07-30 22:45:35 - GakumasuBot - INFO - gui.py:166 - 启动前端服务: npm run dev
2025-07-30 22:45:35 - GakumasuBot - INFO - gui.py:185 - 等待服务就绪: http://127.0.0.1:3000
2025-07-30 22:45:35 - GakumasuBot - INFO - gui.py:192 - 服务就绪: http://127.0.0.1:3000
2025-07-30 22:45:35 - GakumasuBot - INFO - gui.py:260 - 所有服务启动完成，按 Ctrl+C 退出
2025-07-30 22:45:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50530 - "WebSocket /ws" [accepted]
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50534 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50533 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50536 - "GET /screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png HTTP/1.1" 304 Not Modified
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50538 - "GET /screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png HTTP/1.1" 304 Not Modified
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50541 - "WebSocket /ws" [accepted]
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50544 - "GET /api/v1/screenshot/history?limit=100 HTTP/1.1" 200 OK
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50545 - "GET /api/v1/screenshot/stats HTTP/1.1" 200 OK
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50549 - "GET /screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50548 - "GET /screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png HTTP/1.1" 200 OK
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:45:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png -> thumbnails/screenshot_20250727_222715_17f3d56a_thumb.png
2025-07-30 22:46:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_214526_cd186013_thumb.png -> thumbnails/screenshot_20250728_214526_cd186013_thumb.png
2025-07-30 22:46:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png -> thumbnails/screenshot_20250728_223523_73c6bf1a_thumb.png
2025-07-30 22:46:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png -> thumbnails/screenshot_20250729_000238_f7ffab32_thumb.png
2025-07-30 22:46:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png -> thumbnails/screenshot_20250729_211734_2a4065d7_thumb.png
2025-07-30 22:46:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_211838_27d25f15_thumb.png -> thumbnails/screenshot_20250729_211838_27d25f15_thumb.png
2025-07-30 22:46:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213124_8484e77d_thumb.png -> thumbnails/screenshot_20250729_213124_8484e77d_thumb.png
2025-07-30 22:46:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213326_149c5302_thumb.png -> thumbnails/screenshot_20250729_213326_149c5302_thumb.png
2025-07-30 22:46:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png -> thumbnails/screenshot_20250729_213734_72a0b66e_thumb.png
2025-07-30 22:46:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_220149_ddde1861_thumb.png -> thumbnails/screenshot_20250730_220149_ddde1861_thumb.png
2025-07-30 22:46:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png -> thumbnails/screenshot_20250730_224429_b8a94c07_thumb.png
2025-07-30 22:46:24 - GakumasuBot - INFO - gui.py:146 - Backend: [DEBUG] 转换缩略图路径: screenshots/thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png -> thumbnails/screenshot_20250730_224459_cc69ce6a_thumb.png
2025-07-30 22:46:24 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50757 - "GET /api/v1/screenshot/history HTTP/1.1" 200 OK
2025-07-30 22:46:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:32 - GakumasuBot - INFO - gui.py:146 - Backend: INFO:     127.0.0.1:50798 - "GET /screenshots/screenshot_20250729_211838_27d25f15.png HTTP/1.1" 200 OK
2025-07-30 22:46:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:53 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:54 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:55 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:56 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:57 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:58 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:46:59 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:00 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:01 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:02 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:03 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:04 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:05 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:06 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:07 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:08 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:09 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:10 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:11 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:12 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:13 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:14 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:15 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:16 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:17 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:18 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:19 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:20 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:21 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:22 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:23 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:24 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:25 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:26 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:27 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:28 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:29 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:30 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:31 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:32 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:33 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:34 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:35 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:36 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:37 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:38 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:39 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:40 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:41 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:42 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:43 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:44 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:45 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:46 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:47 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:48 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:49 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:50 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:51 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
2025-07-30 22:47:52 - GakumasuBot - INFO - gui.py:146 - Backend: 发送预览帧失败: Cannot call "send" once a close message has been sent.
